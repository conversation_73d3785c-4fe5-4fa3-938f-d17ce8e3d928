<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.min.css">
  <link rel="stylesheet"
    href="https://at.alicdn.com/t/c/font_4001111_4jrdiaeyvuy.css?spm=a313x.7781069.1998910419.52&file=font_4001111_4jrdiaeyvuy.css">
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/wangeditor5/5.1.23/css/style.min.css">
  <link rel="stylesheet" href="./index.css">
  <title>发布文章</title>
</head>

<body>
  <div class="alert info-box">
    操作结果
  </div>
  <div class="wrap">
    <!-- 侧边栏 -->
    <div class="aside">
      <h2>黑马头条</h2>
      <ul class="nav">
        <li>
          <a href="../content/index.html">
            <span class="iconfont icon-16"></span>
            <span>内容管理</span>
          </a>
        </li>
        <li>
          <a href="./index.html" class="active">
            <span class="iconfont icon-fabu"></span>
            <span>发布文章</span>
          </a>
        </li>
      </ul>
    </div>
    <!-- 右侧区域 -->
    <div class="main">
      <!-- 顶部导航 -->
      <div class="top-nav">
        <div class="nav-item">
          <span class="nick-name">昵称</span>
          <span class="quit">退出</span>
        </div>
      </div>
      <!-- 内容区域 -->
      <div class="content">
        <!-- 发布文章 -->
        <div class="card">
          <div class="title">
            <span>发布文章</span>
          </div>
          <div class="body">
            <form class="art-form">
              <input type="text" name="id" hidden>
              <div>
                <label for="title" class="form-label">标题：</label>
                <input type="text" class="form-control" id="title" name="title">
              </div>
              <div>
                <label for="channel_id" class="form-label">频道：</label>
                <select class="form-select" id="channel_id" name="channel_id">
                  <option value="" selected>请选择文章频道</option>
                  <option value="1">频道1</option>
                  <option value="2">频道2</option>
                  <option value="3">频道3</option>
                </select>
              </div>
              <div class="cover">
                <label for="img">封面：</label>
                <label for="img" class="place">+</label>
                <input class="img-file" type="file" name="img" id="img" hidden>
                <img class="rounded">
              </div>
              <div>
                <label for="">内容：</label>
                <!-- 富文本编辑器位置 -->
                <div id="editor—wrapper">
                  <div id="toolbar-container"><!-- 工具栏 --></div>
                  <div id="editor-container"><!-- 编辑器 --></div>
                </div>
                <!-- 记录富文本内容-用于表单收集 -->
                <textarea name="content" class="publish-content" hidden></textarea>
              </div>
              <div>
                <button type="button" class="btn btn-primary send">发布</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.bootcdn.net/ajax/libs/axios/1.3.4/axios.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.min.js"></script>
  <script src="../../lib/form-serialize.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/wangeditor5/5.1.23/index.min.js"></script>
  <script src="../../utils/request.js"></script>
  <script src="../../utils/editor.js"></script>
  <script src="../../utils/auth.js"></script>
  <script src="../../utils/alert.js"></script>
  <script src="./index.js"></script>
</body>

</html>