<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.min.css">
  <link rel="stylesheet"
    href="https://at.alicdn.com/t/c/font_4001111_4jrdiaeyvuy.css?spm=a313x.7781069.1998910419.52&file=font_4001111_4jrdiaeyvuy.css">
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.3/font/bootstrap-icons.min.css">
  <link rel="stylesheet" href="./index.css">
  <title>master分支下的表示</title>
</head>

<body>
  <div class="wrap">
    <!-- 侧边栏 -->
    <div class="aside">
      <h2>黑马头条</h2>
      <ul class="nav">
        <li>
          <a href="./index.html" class="active">
            <span class="iconfont icon-16"></span>
            <span>内容管理</span>
          </a>
        </li>
        <li>
          <a href="../publish/index.html">
            <span class="iconfont icon-fabu"></span>
            <span>发布文章</span>
          </a>
        </li>
      </ul>
    </div>
    <!-- 右侧区域 -->
    <div class="main">
      <!-- 顶部导航 -->
      <div class="top-nav">
        <div class="nav-item">
          <span class="nick-name">昵称</span>
          <span class="quit">退出</span>
        </div>
      </div>
      <!-- 内容区域 -->
      <div class="content">
        <!-- 筛选 -->
        <div class="card">
          <div class="title">
            <span>内容管理</span>
          </div>
          <div class="body">
            <form class="sel-form">
              <div>
                <label class="form-label">状态:</label>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="status" value="" id="all" checked>
                  <label class="form-check-label" for="all">
                    全部
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="status" value="1" id="audit">
                  <label class="form-check-label" for="audit">
                    待审核
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="status" value="2" id="approved">
                  <label class="form-check-label" for="approved">
                    审核通过
                  </label>
                </div>
              </div>
              <div>
                <label class="form-label">频道:</label>
                <select class="form-select" name="channel_id">
                  <option selected>请选择文章频道</option>
                  <option value="1">频道1</option>
                  <option value="2">频道2</option>
                  <option value="3">频道3</option>
                </select>
              </div>
              <div>
                <button type="button" class="btn btn-primary sel-btn">筛选</button>
              </div>
            </form>
          </div>
        </div>
        <!-- 内容列表 -->
        <div class="card">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th>封面</th>
                <th>标题</th>
                <th>状态</th>
                <th>发布时间</th>
                <th>阅读数</th>
                <th>评论数</th>
                <th>点赞数</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody class="align-middle art-list">
              <tr>
                <td>
                  <img
                    src="https://img2.baidu.com/it/u=2640406343,1419332367&fm=253&fmt=auto&app=138&f=JPEG?w=708&h=500"
                    alt="">
                </td>
                <td>文章标题</td>
                <td>
                  <span class="badge text-bg-success">审核通过</span>
                  <span class="badge text-bg-primary">待审核</span>
                </td>
                <td>
                  <span>2023-04-27 10:59:34</span>
                </td>
                <td>
                  <span>0</span>
                </td>
                <td>
                  <span>0</span>
                </td>
                <td>
                  <span>0</span>
                </td>
                <td>
                  <i class="bi bi-pencil-square edit"></i>
                  <i class="bi bi-trash3 del"></i>
                </td>
              </tr>
            </tbody>
          </table>
          <!-- 分页 -->
          <nav>
            <ul class="pagination">
              <li class="page-item last">
                <a class="page-link" href="javascript:;">
                  <span>&laquo;</span>
                </a>
              </li>
              <li class="page-item page-now">
                第1页
              </li>
              <li class="page-item next">
                <a class="page-link" href="javascript:;">
                  <span>&raquo;</span>
                </a>
              </li>
              <li>
                <span class="total-count page-now">共0条</span>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.bootcdn.net/ajax/libs/axios/1.3.4/axios.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.min.js"></script>
  <script src="../../lib/form-serialize.js"></script>
  <script src="../../utils/request.js"></script>
  <script src="../../utils/auth.js"></script>
  <script src="./index.js"></script>
</body>

</html>