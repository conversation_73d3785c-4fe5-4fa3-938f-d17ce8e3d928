html, body{
  height: 100%;
}
.wrap{
  min-height: 100%;
  display: flex;
}

/* 侧边栏 */
.aside {
  width: 200px;
  background-color: #023;
  padding-top: 40px;
}
.aside h2 {
  text-align: center;
  color: #fc6627;
}

/* 侧边栏导航 */
.nav{
  padding-left: 20px;
}
.nav li {
  width: 100%;
  height: 60px;
  line-height: 60px;
  color: white;
  cursor: pointer;
  list-style: none;
}
.nav li a{
  color: white;
  text-decoration: none;
}
.nav li a.active{
  color: #409eff !important;
}

/* 内容区域 */
.main{
  height: 100%;
  flex: 1;
}
.top-nav{
  height: 60px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 40px;
}
.nav-item span{
  cursor: pointer;
}
.nav-item span:last-of-type{
  margin-left: 20px;
}

/* 卡片容器布局 */
.content{
  padding: 30px;
}
.card{
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border: 1px solid #ebeef5;
  padding: 20px;
}
.card:nth-child(n + 2){
  margin-top: 30px;
}
.card .title{
  height: 50px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
  
}

/* 表单部分 */
.form-check, .form-select{
  display: inline-block;
  margin-left: 10px;
}
.form-select{
  width: auto;
}
.body form>div {
  margin-top: 20px;
}

/* 表格列表部分 */
.table img {
  width: 100px;
  height: 100px;
}
.table .bi{
  cursor: pointer;
}

/* 分页 */
.page-now{
  line-height: 40px;
  padding: 0 20px;
}